'use client'
import { useEffect, useState } from 'react'
import { TableSkeleton } from '@/components/skeletons'
import Link from 'next/link'
import { getSeaLogsGroups } from '@/app/lib/actions'
import { preventCrewAccess } from '@/app/helpers/userHelper'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { H3 } from '@/components/ui'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { Plus } from 'lucide-react'

const UserRoleList = () => {
    const router = useRouter()
    const [userRoles, setUserRoles] = useState([])
    getSeaLogsGroups(setUserRoles)

    useEffect(() => {
        preventCrewAccess()
    }, [])

    // Define columns for the user roles table
    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Role" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const userRole: any = row.original
                return (
                    <Link
                        href={`/settings/user-role/edit?id=${userRole.id}`}
                        className="hover:underline">
                        {userRole.title}
                    </Link>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.title || ''
                const valueB = rowB?.original?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'code',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Code" />
            ),
            cellAlignment: 'center' as const,
            cell: ({ row }: { row: any }) => {
                const userRole: any = row.original
                return <span>{userRole.code}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.code || ''
                const valueB = rowB?.original?.code || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'description',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Description" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const userRole: any = row.original
                return <span>{userRole.description}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.description || ''
                const valueB = rowB?.original?.description || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <div className="w-full p-0">
            <div className="flex justify-between items-center">
                <H3>User Roles</H3>
                <Button
                    onClick={() => {
                        router.push(`/settings/user-role/create`)
                    }}
                    iconLeft={Plus}>
                    New User Role
                </Button>
            </div>
            <div className="pt-4">
                <div className="flex w-full justify-start flex-col md:flex-row items-start">
                    {!userRoles ? (
                        <TableSkeleton />
                    ) : (
                        <DataTable
                            columns={columns}
                            data={userRoles}
                            showToolbar={false}
                            pageSize={10}
                            showPageSizeSelector={true}
                        />
                    )}
                </div>
            </div>
        </div>
    )
}

export default UserRoleList
