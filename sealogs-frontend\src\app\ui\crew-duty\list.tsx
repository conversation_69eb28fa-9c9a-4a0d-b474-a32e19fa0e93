'use client'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { TableSkeleton } from '@/components/skeletons'
import { getCrewDuties } from '@/app/lib/actions'
import { preventCrewAccess } from '@/app/helpers/userHelper'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { H2 } from '@/components/ui'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { Plus } from 'lucide-react'

const CrewDutyList = () => {
    const router = useRouter()
    const [crewDuties, setCrewDuties] = useState([])

    const handleSetDuties = (duties: any) => {
        const activeDuties = duties.filter((duty: any) => !duty.archived)
        setCrewDuties(activeDuties)
    }
    getCrewDuties(handleSetDuties)

    useEffect(() => {
        preventCrewAccess()
    }, [])

    // Define columns for the crew duties table
    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Title" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const crewDuty: any = row.original
                return (
                    <Link
                        href={`/settings/crew-duty/edit?id=${crewDuty.id}`}
                        className="hover:underline">
                        {crewDuty.title}
                    </Link>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.title || ''
                const valueB = rowB?.original?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'abbreviation',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Abbreviation" />
            ),
            cellAlignment: 'center' as const,
            cell: ({ row }: { row: any }) => {
                const crewDuty: any = row.original
                return <span>{crewDuty.abbreviation}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.abbreviation || ''
                const valueB = rowB?.original?.abbreviation || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <div className="w-full p-0">
            <div className="flex justify-between items-center">
                <H2 className="text-3xl">Crew Duties</H2>
                <Button
                    onClick={() => router.push(`/settings/crew-duty/create`)}
                    iconLeft={Plus}>
                    New Crew Duty
                </Button>
            </div>
            <div className="pt-4">
                <div className="flex w-full justify-start flex-col md:flex-row items-start">
                    {!crewDuties ? (
                        <TableSkeleton />
                    ) : (
                        <DataTable
                            columns={columns}
                            data={crewDuties}
                            showToolbar={false}
                            pageSize={10}
                            showPageSizeSelector={true}
                        />
                    )}
                </div>
            </div>
        </div>
    )
}

export default CrewDutyList
