'use client'
import React, { useState } from 'react'
import Link from 'next/link'
import { getMaintenanceCategory } from '@/app/lib/actions'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import TableWrapper from '@/components/ui/table-wrapper'
import PopoverWrapper from '@/components/ui/popover-wrapper'
import { Rows4Icon } from 'lucide-react'
import {
    Button,
    H1,
    ListHeader,
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui'

export default function MaintenanceCategoryList() {
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    const [categories, setCategories] = useState<any>()

    getMaintenanceCategory(setCategories)

    return (
        <div className="w-full p-0  ">
            <div className="flex justify-between pb-4 items-center">
                <H1>Maintenance Categories</H1>
            </div>
            <ListHeader
                title="Maintenance Categories"
                actions={
                    <Button
                        onClick={() =>
                            router.push(`/settings/maintenance/category/new`)
                        }>
                        Add New
                    </Button>
                }
            />
            <div className="pt-4">
                <div className="flex w-full justify-start flex-col md:flex-row items-start">
                    {categories && (
                        <TableWrapper
                            headings={[
                                'Name:firstHead',
                                'Abbreviation',
                                'Recent Maintenance tasks:center',
                            ]}>
                            {categories.map((category: any) => (
                                <tr
                                    key={category.id}
                                    className={`group border-b  hover: `}>
                                    <td className="px-2 py-3 lg:px-6">
                                        <div className="flex items-center">
                                            <span
                                                className={` text-foreground ${category.archived == true ? 'line-through ' : ''}`}>
                                                <Link
                                                    href={`/settings/maintenance/category?categoryID=${category.id}`}
                                                    className="">
                                                    {category.name}
                                                </Link>
                                            </span>
                                        </div>
                                    </td>
                                    <td className="px-2 py-3">
                                        {category.abbreviation}
                                    </td>
                                    <td className="px-2 py-3 text-center">
                                        {category?.componentMaintenanceCheck
                                            ?.nodes.length > 0 && (
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <Button
                                                        size={'icon'}
                                                        variant={
                                                            'primaryOutline'
                                                        }>
                                                        <Rows4Icon />
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent>
                                                    {category.componentMaintenanceCheck.nodes.map(
                                                        (maintenance: any) => (
                                                            <Link
                                                                key={
                                                                    maintenance
                                                                }
                                                                href={`/maintenance?taskID=${maintenance.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                                                className="block py-2 ps-2 ">
                                                                {
                                                                    maintenance.name
                                                                }
                                                            </Link>
                                                        ),
                                                    )}
                                                </PopoverContent>
                                            </Popover>
                                        )}
                                    </td>
                                </tr>
                            ))}
                        </TableWrapper>
                    )}
                </div>
            </div>
        </div>
    )
}
