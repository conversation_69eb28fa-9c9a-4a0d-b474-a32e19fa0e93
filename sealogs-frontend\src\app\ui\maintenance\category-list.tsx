'use client'
import React, { useState } from 'react'
import Link from 'next/link'
import { getMaintenanceCategory } from '@/app/lib/actions'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { Rows4Icon } from 'lucide-react'
import {
    But<PERSON>,
    H1,
    ListHeader,
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'

export default function MaintenanceCategoryList() {
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    const [categories, setCategories] = useState<any>()

    getMaintenanceCategory(setCategories)

    // Define columns for the maintenance categories table
    const columns = createColumns([
        {
            accessorKey: 'name',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Name" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const category: any = row.original
                return (
                    <div className="flex items-center">
                        <span
                            className={`text-foreground ${category.archived === true ? 'line-through' : ''}`}>
                            <Link
                                href={`/settings/maintenance/category?categoryID=${category.id}`}
                                className="hover:underline">
                                {category.name}
                            </Link>
                        </span>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'abbreviation',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Abbreviation" />
            ),
            cellAlignment: 'center' as const,
            cell: ({ row }: { row: any }) => {
                const category: any = row.original
                return <span>{category.abbreviation}</span>
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.abbreviation || ''
                const valueB = rowB?.original?.abbreviation || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'componentMaintenanceCheck',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader
                    column={column}
                    title="Recent Maintenance Tasks"
                />
            ),
            cellAlignment: 'center' as const,
            cell: ({ row }: { row: any }) => {
                const category: any = row.original
                return (
                    <>
                        {category?.componentMaintenanceCheck?.nodes.length >
                            0 && (
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        size={'icon'}
                                        variant={'primaryOutline'}>
                                        <Rows4Icon />
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent>
                                    {category.componentMaintenanceCheck.nodes.map(
                                        (maintenance: any) => (
                                            <Link
                                                key={maintenance.id}
                                                href={`/maintenance?taskID=${maintenance.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                                className="block py-2 ps-2 hover:underline">
                                                {maintenance.name}
                                            </Link>
                                        ),
                                    )}
                                </PopoverContent>
                            </Popover>
                        )}
                    </>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA =
                    rowA?.original?.componentMaintenanceCheck?.nodes?.length ||
                    0
                const valueB =
                    rowB?.original?.componentMaintenanceCheck?.nodes?.length ||
                    0
                return valueA - valueB
            },
        },
    ])

    return (
        <div className="w-full p-0">
            <div className="flex justify-between pb-4 items-center">
                <H1>Maintenance Categories</H1>
            </div>
            <ListHeader
                title="Maintenance Categories"
                actions={
                    <Button
                        onClick={() =>
                            router.push(`/settings/maintenance/category/new`)
                        }>
                        Add New
                    </Button>
                }
            />
            <div className="pt-4">
                <div className="flex w-full justify-start flex-col md:flex-row items-start">
                    {categories && (
                        <DataTable
                            columns={columns}
                            data={categories}
                            showToolbar={false}
                            pageSize={10}
                            showPageSizeSelector={true}
                        />
                    )}
                </div>
            </div>
        </div>
    )
}
